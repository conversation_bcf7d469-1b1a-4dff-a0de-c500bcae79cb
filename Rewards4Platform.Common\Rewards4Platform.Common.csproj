﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
    <PackageReference Include="Polly" Version="8.6.2" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.58" />
    <PackageReference Include="StackExchange.Redis.Extensions.AspNetCore" Version="11.0.0" />
    <PackageReference Include="StackExchange.Redis.Extensions.Core" Version="11.0.0" />
    <PackageReference Include="StackExchange.Redis.Extensions.Newtonsoft" Version="11.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Constants\" />
  </ItemGroup>

</Project>
