﻿using Rewards4Platform.API.Constants;
using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Responses;
using StackExchange.Redis.Extensions.Core.Abstractions;

namespace Rewards4Platform.API.Services
{
    public interface IMerchantService
    {
        Task<IList<int>> GetBlockedMerchantIds(string applicationName, int clubId);
        Task<bool> IsValidMerchant(string urlName, string applicationName, int clubId);
        Task<MerchantDetailsResponse> GetMerchantDetails(string merchantUrlName, string applicationName, int clubId);
    }

    public class MerchantService : IMerchantService
    {
        private readonly IMerchantRepository _merchantRepository;
        private readonly IRedisDatabase _redis;

        public MerchantService(IMerchantRepository merchantRepository, IRedisDatabase redis)
        {
            _merchantRepository = merchantRepository;
            _redis = redis;
        }

        public async Task<IList<int>> GetBlockedMerchantIds(string applicationName, int clubId)
        {
            var cacheKey = CacheKey.Merchant.SuppressedMerchants(applicationName, clubId);

            if (await _redis.ExistsAsync(cacheKey))
            {
                return await _redis.GetWithRetryAsync<IList<int>>(cacheKey) ?? new List<int>();
            }                

            var blockedMerchantIds = await _merchantRepository.GetBlockedMerchantIds(applicationName, clubId).ConfigureAwait(false);

            await _redis.AddAsync(cacheKey, blockedMerchantIds, CacheDuration.Merchant.SuppressedMerchants).ConfigureAwait(false);

            return blockedMerchantIds ?? new List<int>();
        }

        public async Task<bool> IsValidMerchant(string urlName, string applicationName, int clubId)
        {
            return await _merchantRepository.IsValidMerchant(urlName, applicationName, clubId).ConfigureAwait(false);
        }

        public async Task<MerchantDetailsResponse> GetMerchantDetails(string merchantUrlName, string applicationName, int clubId)
        {
            var merchantDetails = new MerchantDetailsResponse();

            if (string.IsNullOrWhiteSpace(merchantUrlName))
            {
                merchantDetails =  new MerchantDetailsResponse
                {
                    IsSuccess = false,
                    Message = "Merchant URL name cannot be null or empty."
                };

                return merchantDetails;
            }

            var isValidMerchant = await IsValidMerchant(merchantUrlName, applicationName, clubId);

            if (!isValidMerchant)
            {
                merchantDetails = new MerchantDetailsResponse
                {
                    IsSuccess = false,
                    Message = "The requested merchant is not found or suppressed."
                };

                return merchantDetails;
            }

            var cacheKey = CacheKey.Merchant.MerchantDetails(merchantUrlName, applicationName, clubId);

            if (await _redis.ExistsAsync(cacheKey))
            {
                merchantDetails = await _redis.GetWithRetryAsync<MerchantDetailsResponse>(cacheKey);

                return merchantDetails!;
            }

            merchantDetails = await _merchantRepository.GetMerchantDetails(merchantUrlName).ConfigureAwait(false);

            if (merchantDetails.IsSuccess)
            {
                merchantDetails.IsCustomRetailer = false;

                await _redis.AddAsync(cacheKey, merchantDetails, CacheDuration.Merchant.MerchantDetails).ConfigureAwait(false);

                return merchantDetails;
            }

            // TODO:if merchant details not found in the database, fetch it from CMS

            return new MerchantDetailsResponse
            {
                IsSuccess = false,
                Message = "Merchant details not found."
            };
        }        
    }
}
