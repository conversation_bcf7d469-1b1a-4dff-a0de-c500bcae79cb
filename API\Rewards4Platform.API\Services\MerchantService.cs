﻿using Rewards4Platform.API.Constants;
using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Responses;
using StackExchange.Redis.Extensions.Core.Abstractions;

namespace Rewards4Platform.API.Services
{
    public interface IMerchantService
    {
        Task<IList<int>> GetBlockedMerchantIds(string applicationName, int clubId);
        Task<bool> IsValidMerchant(string urlName, string applicationName, int clubId);
        Task<MerchantDetailsResponse> GetMerchantDetails(string merchantUrlName, string applicationName, int clubId);
    }

    public class MerchantService : IMerchantService
    {
        private readonly IMerchantRepository _merchantRepository;
        private readonly IRedisDatabase _redis;

        public MerchantService(IMerchantRepository merchantRepository, IRedisDatabase redis)
        {
            _merchantRepository = merchantRepository;
            _redis = redis;
        }

        public async Task<IList<int>> GetBlockedMerchantIds(string applicationName, int clubId)
        {
            var cacheKey = CacheKey.Merchant.SuppressedMerchants(applicationName, clubId);

            if (await _redis.ExistsAsync(cacheKey))
            {
                return await _redis.GetWithRetryAsync<IList<int>>(cacheKey) ?? new List<int>();
            }                

            var blockedMerchantIds = await _merchantRepository.GetBlockedMerchantIds(applicationName, clubId).ConfigureAwait(false);

            await _redis.AddAsync(cacheKey, blockedMerchantIds, CacheDuration.Merchant.SuppressedMerchants).ConfigureAwait(false);

            return blockedMerchantIds ?? new List<int>();
        }

        public async Task<bool> IsValidMerchant(string urlName, string applicationName, int clubId)
        {
            return await _merchantRepository.IsValidMerchant(urlName, applicationName, clubId).ConfigureAwait(false);
        }

        public async Task<MerchantDetailsResponse> GetMerchantDetails(string merchantUrlName, string applicationName, int clubId)
        {
            var merchantDetailsResponse = new MerchantDetailsResponse();

            if (string.IsNullOrWhiteSpace(merchantUrlName))
            {
                merchantDetailsResponse =  new MerchantDetailsResponse
                {
                    IsSuccess = false,
                    Message = "Merchant URL name cannot be null or empty."
                };

                return merchantDetailsResponse;
            }

            var isValidMerchant = await IsValidMerchant(merchantUrlName, applicationName, clubId);

            if (!isValidMerchant)
            {
                merchantDetailsResponse = new MerchantDetailsResponse
                {
                    IsSuccess = false,
                    Message = "The requested merchant is not found or suppressed."
                };

                return merchantDetailsResponse;
            }

            var cacheKey = CacheKey.Merchant.MerchantDetails(merchantUrlName, applicationName, clubId);

            if (await _redis.ExistsAsync(cacheKey))
            {
                merchantDetailsResponse = await _redis.GetWithRetryAsync<MerchantDetailsResponse>(cacheKey);

                return merchantDetailsResponse!;
            }

            merchantDetailsResponse = await _merchantRepository.GetMerchantDetails(merchantUrlName).ConfigureAwait(false);

            if (merchantDetailsResponse.IsSuccess)
            {
                merchantDetailsResponse.IsCustomRetailer = false;

                merchantDetailsResponse.CustomDescription = "Enjoy collecting points when you shop online with @Model.Retailer.Name. Just remember to click through us here first so we can track your purchase and reward you the right amount of points.";

                var defaultHeaderBackgroundImageUrl = "https://neutstr4gblb.blob.core.windows.net/rewards4sport/generic-retailer-header.jpg";
                merchantDetailsResponse.HeaderBrackgroungImageUrl = string.IsNullOrWhiteSpace(merchantDetailsResponse.HeaderBrackgroungImageUrl) ? defaultHeaderBackgroundImageUrl : merchantDetailsResponse.HeaderBrackgroungImageUrl;

                await _redis.AddAsync(cacheKey, merchantDetailsResponse, CacheDuration.Merchant.MerchantDetails).ConfigureAwait(false);

                return merchantDetailsResponse;
            }

            // TODO:if merchant details not found in the database, fetch it from CMS

            return new MerchantDetailsResponse
            {
                IsSuccess = false,
                Message = "Merchant details not found."
            };
        }        
    }
}
