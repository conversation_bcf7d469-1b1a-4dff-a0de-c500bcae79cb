{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "Rewards4Platform": "Data Source=NEU-TSTDEV-SQL3\\TEST;Initial Catalog=Rewards4Platform;Persist Security Info=True;Integrated Security=SSPI;TrustServerCertificate=True;",
    "Membership": "Data Source=NEU-TSTDEV-SQL3\\TEST;Initial Catalog=Membership;Persist Security Info=True;Integrated Security=SSPI;TrustServerCertificate=True;"
  },
  "Jwt": {
    "Issuer": "rewards4platform",
    "Audience": "rewards4platform",
    "SecretKey": "a7c9f3e1-4b28-47da-b1c5-82d94e3fa6b2-xjd83ks92lqp09mn-zke82md9fjr472"
  },
  "Web": {
    //"Url": "http://localhost:8080",
    "Url": "https://neu-r4g-app-rewards4platform-web-testing.azurewebsites.net"
  },
  "Redis": {
    "_description": "This is the redis connection strings for the website and cms",
    "ServerName": "localhost",
    "Port": "6379",
    "Password": "",
    "UseSsl": "false",
    "Website": {
      "KeyPrefix": "rewards4platform:dev:website:",
      "Database": "3"
    },
    "Cms": {
      "Database": "3",
      "KeyPrefix": "rewards4platform:dev:cms:"
    }
  },
  "Exceptionless": {
    "_description": "Exceptionless is the service we use to record exceptions",
    "ApiKey": "7uuhSGy5kiAZxid5MMPw4tT24Rv5EDXqNxH9frOU"
  }
}
