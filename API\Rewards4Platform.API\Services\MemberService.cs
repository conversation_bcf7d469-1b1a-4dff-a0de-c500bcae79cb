﻿using Rewards4Platform.API.Data.Repositories;
using Rewards4Platform.API.Models;
using Rewards4Platform.API.Requests;
using Rewards4Platform.API.Responses;

namespace Rewards4Platform.API.Services
{
    public interface IMemberService
    {
        Task<RegisterMemberResponse> Register(RegistrationRequest request);
        Task<LoginMemberResponse> Login(LoginRequest request);
        Task<User?> GetMemberByEmail(string email);
    }

    public class MemberService : IMemberService
    {
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly IUserRepository _userRepository;
        private readonly IJwtProvider _jwtProvider;

        public MemberService(IPasswordHashingService passwordHashingService, IUserRepository userRepository, IJwtProvider jwtProvider)
        {
            _passwordHashingService = passwordHashingService;
            _userRepository = userRepository;
            _jwtProvider = jwtProvider;
        }        

        public async Task<RegisterMemberResponse> Register(RegistrationRequest request)
        {
            // validate the request


            // Create a new member
            var hashedPassword = await _passwordHashingService.HashPassword(request.Password);

            var passwordElements = hashedPassword.Split(':');

            var hash = passwordElements[0];
            var salt = passwordElements[1];

            var newId = Guid.NewGuid();

            var newUser = new User
            {
                Id = newId,
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                Password = hash,
                Salt = salt,
                InsertDate = DateTime.Now,
                UpdateDate = null
            };

            var isSuccessful = await _userRepository.CreateUser(newUser);

            if (isSuccessful)
            {
                var token = await _jwtProvider.GenerateToken(newUser);

                return new RegisterMemberResponse
                {
                    IsSuccess = true,
                    UserId = newId,
                    Message = "User registered successfully.",
                    Token = token,
                    IsDeepLinkLogin = false
                };
            }

            return new RegisterMemberResponse
            {
                IsSuccess = false,
                UserId = Guid.Empty,
                Message = "User registration failed.",
                Token = string.Empty,
                IsDeepLinkLogin = false
            };
        }

        public async Task<LoginMemberResponse> Login(LoginRequest request)
        {
            var user = await GetMemberByEmail(request.Email);

            if (user is not null)
            {
                var isValidPassword = await _passwordHashingService.VerifyPassword(user.Password,
                                                                               user.Salt,
                                                                               request.Password);

                if (isValidPassword)
                {
                    // Generate JWT
                    var token = await _jwtProvider.GenerateToken(user);

                    return new LoginMemberResponse
                    {
                        IsSuccess = true,
                        UserId = user.Id,
                        Message = "Login successful.",
                        Token = token,
                        IsDeepLinkLogin = false
                    };
                }
            }

            return new LoginMemberResponse
            {
                IsSuccess = false,
                UserId = Guid.Empty,
                Message = "Invalid email or password.",
                Token = string.Empty,
                IsDeepLinkLogin = false
            };
        }

        public async Task<User?> GetMemberByEmail(string email)
        {
            return await _userRepository.GetUserByEmail(email);
        }
    }
}
