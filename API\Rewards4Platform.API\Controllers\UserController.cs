﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rewards4Platform.API.Requests;
using Rewards4Platform.API.Responses;
using Rewards4Platform.API.Services;

namespace Rewards4Platform.API.Controllers
{
    [Route("api/user")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IMemberService _userService;
        private readonly ICommonService _commonService;

        private const string AuthenticationTokenCookieName = "jwt";

        public UserController(IMemberService userService, ICommonService commonService)
        {
            _userService = userService;
            _commonService = commonService;
        }        

        [HttpPost]
        [Route("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register([FromBody] RegistrationRequest request)
        {
            var result = await _userService.Register(request);

            if (result.IsSuccess)
            {
                var cookieOptions = _commonService.SetCookieOptions();

                Response.Cookies.Append(AuthenticationTokenCookieName, result.Token, cookieOptions);

                return CreatedAtAction(null, new RegisterUserResponse { IsSuccess = true });
            }

            return Ok(new { result.IsSuccess, result.Message });
        }

        [HttpPost]
        [Route("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            var result = await _userService.Login(request);

            if (result.IsSuccess)
            {
                var cookieOptions = _commonService.SetCookieOptions();                

                Response.Cookies.Append(AuthenticationTokenCookieName, result.Token, cookieOptions);

                return Ok(new { result.IsSuccess, result.UserId, result.FirstName, result.LastName, result.Message });
            }

            return Ok(new { result.IsSuccess, result.Message });
        }

        [HttpPost]
        [Route("logout")]
        public IActionResult Logout()
        {
            var cookieOptions = _commonService.SetCookieOptions();

            Response.Cookies.Delete(AuthenticationTokenCookieName, cookieOptions);

            return Ok();
        }
    }
}
