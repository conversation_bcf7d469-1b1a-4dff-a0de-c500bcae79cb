﻿using Microsoft.AspNetCore.Authentication;
using Rewards4Platform.API.Data;
using Rewards4Platform.API.Data.Membership;
using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Data.Repositories;
using Rewards4Platform.API.Exceptions;
using Rewards4Platform.API.Services;

namespace Rewards4Platform.API.Extensions
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddDependencyInjection(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
        {

            services.AddSingleton<ISqlConnectionFactory, SqlConnectionFactory>();
            services.AddSingleton<IMembershipDbSqlConnectionFactory, MembershipDbSqlConnectionFactory>();

            services.AddScoped<IJwtProvider, JwtProvider>();

            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IPasswordHashingService, PasswordHashingService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ICommonService, CommonService>();
            services.AddScoped<IMerchantService, MerchantService>();

            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IMerchantRepository, MerchantRepository>();            

            services.AddSingleton<IExceptionLogger, ExceptionLogger>();

            return services;
        }
    }
}
