<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Exceptionless.AspNetCore" Version="6.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.18" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Rewards4Platform.Common\Rewards4Platform.Common.csproj" />
  </ItemGroup>

</Project>
