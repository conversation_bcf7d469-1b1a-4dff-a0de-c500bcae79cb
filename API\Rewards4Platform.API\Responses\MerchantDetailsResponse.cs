﻿using Microsoft.AspNetCore.Authentication;

namespace Rewards4Platform.API.Responses
{
    public class MerchantDetailsResponse : ApiResponse
    {
        public string MerchantName { get; set; } = string.Empty;
        public string UrlName { get; set; } = string.Empty;
        public string MerchantLogoUrl { get; set; } = string.Empty;
        public string HeaderBrackgroungImageUrl { get; set; } = string.Empty;  
        public string CustomDescription { get; set; } = string.Empty;
        public bool IsCustomRetailer { get; set; }
        public List<RetailerRate> Rates { get; set; } = new List<RetailerRate>();
        public MerchantTermsAndConditions TermsAndConditions { get; set; } = new MerchantTermsAndConditions();
        public bool IsSafariMerchant { get; set; }
    }

    public class RetailerRate
    {
        public string Rate { get; set; } = string.Empty; 
        public string Description { get; set; } = string.Empty;
        public int OfferId { get; set; }
        public string LinkText { get; set; } = "Shop to collect points";
    }

    public class MerchantTermsAndConditions
    {
        public List<string> Terms { get; set; } = new List<string>();
        public List<string> Exclusions { get; set; } = new List<string>();
    }
}
