﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Rewards4Platform.API.Requests;
using Rewards4Platform.API.Responses;
using Rewards4Platform.API.Services;

namespace Rewards4Platform.API.Controllers
{
    [Route("api/authentication")]
    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly IMemberService _userService;
        private readonly ICommonService _commonService;

        private const string AuthenticationTokenCookieName = "jwt";

        public AuthenticationController(IMemberService userService, ICommonService commonService)
        {
            _userService = userService;
            _commonService = commonService;
        }

        [HttpPost]
        [Route("register")]
        [AllowAnonymous]
        public async Task<IActionResult> RegisterMember([FromBody] RegistrationRequest request)
        {
            var result = await _userService.Register(request);

            if (result.IsSuccess)
            {
                var cookieOptions = _commonService.SetCookieOptions();

                Response.Cookies.Append(AuthenticationTokenCookieName, result.Token, cookieOptions);

                return CreatedAtAction(null, new RegisterUserResponse { IsSuccess = true });
            }

            return Ok(new { result.IsSuccess, result.Message });
        }
    }
}
