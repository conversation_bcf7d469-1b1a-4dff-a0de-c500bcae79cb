﻿using Dapper;
using Rewards4Platform.API.Responses;
using System.Data;

namespace Rewards4Platform.API.Data.Membership.Repositories
{
    public interface IMerchantRepository
    {
        Task<IList<int>> GetBlockedMerchantIds(string applicationName, int clubId);
        Task<bool> IsValidMerchant(string urlName, string applicationName, int clubId);
        Task<MerchantDetailsResponse> GetMerchantDetails(string urlName);
    }

    public class MerchantRepository : IMerchantRepository
    {
        private readonly IMembershipDbSqlConnectionFactory _membershipDbConnectionFactory;

        public MerchantRepository(IMembershipDbSqlConnectionFactory connectionFactory)
        {
            _membershipDbConnectionFactory = connectionFactory;
        }

        public async Task<IList<int>> GetBlockedMerchantIds(string applicationName, int clubId)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Website_GetSuppressedMerchantIds";

            var parameters = new
            {
                applicationName,
                clubId
            };

            var blockedMerchantIds = await sqlConnection.QueryAsync<int>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return blockedMerchantIds.ToList();
        }        

        public async Task<bool> IsValidMerchant(string urlName, string applicationName, int clubId)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Website_IsValidMerchant";

            var parameters = new
            {
                urlName,
                applicationName,
                clubId
            };

            var isBlockedMerchant = await sqlConnection.ExecuteScalarAsync<bool>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return isBlockedMerchant;
        }

        public async Task<MerchantDetailsResponse> GetMerchantDetails(string urlName)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Website_GetMerchantDetails";

            var parameters = new
            {
                UrlName = urlName
            };

            var response = new MerchantDetailsResponse();

            using var multi = await sqlConnection.QueryMultipleAsync(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            // 1. Merchant Basic Info
            var merchantInfo = await multi.ReadFirstOrDefaultAsync();
            if (merchantInfo == null)
            {
                return new MerchantDetailsResponse
                {
                    IsSuccess = false,
                    Message = "Merchant not found."
                };
            }

            response.MerchantName = merchantInfo.Name;
            response.UrlName = merchantInfo.Url;
            response.IsSafariMerchant = merchantInfo.IsSafariMerchant;

            // 2. Terms
            var terms = (await multi.ReadAsync<string>()).ToList();
            response.TermsAndConditions.Terms = terms;

            // 3. Exclusions
            var exclusions = (await multi.ReadAsync<string>()).ToList();
            response.TermsAndConditions.Exclusions = exclusions;

            // 4. Rates
            var rates = await multi.ReadAsync<RetailerRate>();
            response.Rates = rates.ToList();

            // 5. Square Logo
            var logo = await multi.ReadFirstOrDefaultAsync();
            if (logo != null && logo?.SquareLogo != null)
            {
                response.MerchantLogoUrl = logo!.SquareLogo;
            }

            // 6. Header Background Image
            var headerBackgroundImage = await multi.ReadFirstOrDefaultAsync();
            if (headerBackgroundImage != null && headerBackgroundImage?.HeaderBackgroundImage != null)
            {
                response.HeaderBrackgroungImageUrl = headerBackgroundImage!.HeaderBackgroundImage;
            }

            response.IsSuccess = true;

            return response;
        }
    }
}
