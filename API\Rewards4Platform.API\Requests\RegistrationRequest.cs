﻿namespace Rewards4Platform.API.Requests
{
    public class RegistrationRequest
    {
        public string Username { get; set; } = string.Empty;  
        public string Password { get; set; } = string.Empty;
        public string Forename { get; set; } = string.Empty;       
        public string SourceUrl { get; set; } = string.Empty;
        public bool AccountExists { get; set; }        
        public string RedirectUrl { get; set; } = string.Empty;
        public int? MemberReferralId { get; set; }
        public string SingleUseCode { get; set; } = string.Empty;
    }
}
